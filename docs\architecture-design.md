# 架构设计文档

## 系统概述

软件许可证验证系统采用现代化微服务架构，基于 Cloudflare Workers 无服务器平台，提供高性能、高可用的许可证验证服务。

## 技术选型

### 1. 后端技术栈

- **运行环境**: Cloudflare Workers
- **Web 框架**: Hono.js (轻量级、高性能)
- **数据库**: Cloudflare D1 (SQLite 兼容)
- **ORM**: Prisma (类型安全、开发效率高)
- **语言**: TypeScript (类型安全、开发体验好)

### 2. 前端技术栈

- **框架**: React 18 + Next.js 14
- **UI 组件**: Ant Design (企业级 UI 组件库)
- **图表**: ECharts (数据可视化)
- **状态管理**: React Query + Zustand
- **样式**: Tailwind CSS + CSS Modules

### 3. 开发工具链

- **包管理**: pnpm (快速、节省空间)
- **构建工具**: Vite (快速构建)
- **测试框架**: Vitest (快速单元测试)
- **代码质量**: ESLint + Prettier + TypeScript

### 4. 部署平台

- **后端**: Cloudflare Workers (全球边缘计算)
- **前端**: Vercel (自动化部署、CDN 加速)
- **数据库**: Cloudflare D1 (全球分布式)
- **静态资源**: Cloudflare R2 (对象存储)

## 系统架构

### 1. 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理后台      │    │   客户端SDK     │    │   最终用户软件   │
│   (Next.js)     │    │  (多语言支持)    │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ HTTPS/REST API        │ HTTPS/REST API        │ HTTPS/REST API
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  API Gateway    │
                    │ (Cloudflare)    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Workers API    │
                    │   (Hono.js)     │
                    └─────────────────┘
                                 │
                ┌────────────────┼────────────────┐
                │                │                │
       ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
       │   D1 Database   │ │   KV Storage    │ │   R2 Storage    │
       │   (SQLite)      │ │    (Cache)      │ │   (Files)       │
       └─────────────────┘ └─────────────────┘ └─────────────────┘
```

### 2. 分层架构

#### 2.1 表现层 (Presentation Layer)

- **管理后台**: React + Next.js + Ant Design

  - 用户管理界面
  - 产品管理界面
  - 授权管理界面
  - 许可证管理界面
  - 数据统计界面

- **客户端 SDK**: 多语言支持
  - JavaScript/TypeScript SDK
  - Python SDK
  - C# SDK
  - Java SDK

#### 2.2 业务逻辑层 (Business Logic Layer)

- **用户服务**: 用户认证、权限管理
- **产品服务**: 产品和版本管理
- **授权服务**: 分发商授权管理
- **许可证服务**: 许可证生成、激活、验证
- **统计服务**: 数据统计和报表

#### 2.3 数据访问层 (Data Access Layer)

- **Prisma ORM**: 类型安全的数据库操作
- **数据库连接池**: 优化数据库连接
- **缓存层**: KV Store 缓存热点数据

#### 2.4 基础设施层 (Infrastructure Layer)

- **Cloudflare Workers**: 无服务器计算平台
- **Cloudflare D1**: 分布式 SQLite 数据库
- **Cloudflare KV**: 全球分布式键值存储
- **Cloudflare R2**: 对象存储服务

### 3. 数据库设计

#### 3.1 核心实体关系

```
User (用户)
├── role: ADMIN | DISTRIBUTOR
├── status: ACTIVE | INACTIVE
└── 关联: licenses[], authorizations[]

Product (产品)
├── status: ACTIVE | INACTIVE
└── 关联: versions[]

ProductVersion (产品版本)
├── verifyTemplate: JSON配置
├── encryptionKey: 加密密钥
└── 关联: product, licenses[], authorizations[]

Authorization (授权)
├── status: ACTIVE | INACTIVE
└── 关联: distributor(User), version(ProductVersion)

License (许可证)
├── status: INACTIVE | ACTIVE | REVOKED
├── verifyInstance: 验证实例绑定
└── 关联: version(ProductVersion), distributor(User)
```

#### 3.2 索引策略

- 主键索引: 所有表的 id 字段
- 唯一索引: username, licenseKey
- 复合索引: (role, status), (distributorId, status)
- 时间索引: createdAt, updatedAt, activatedAt

### 4. 安全架构

#### 4.1 认证授权

- **JWT Token**: 无状态认证
- **角色权限**: RBAC 权限模型
- **Token 刷新**: 自动续期机制
- **会话管理**: 安全会话控制

#### 4.2 数据安全

- **密码加密**: bcrypt 哈希算法
- **传输加密**: HTTPS/TLS 1.3
- **数据加密**: 敏感数据字段加密
- **访问控制**: 细粒度权限控制

#### 4.3 接口安全

- **请求限流**: 防止 API 滥用
- **参数验证**: 严格的输入验证
- **CORS 配置**: 跨域请求控制
- **安全头**: 安全 HTTP 头设置

### 5. 性能优化

#### 5.1 缓存策略

- **KV 缓存**: 热点数据缓存
- **CDN 缓存**: 静态资源缓存
- **浏览器缓存**: 客户端缓存
- **数据库缓存**: 查询结果缓存

#### 5.2 数据库优化

- **索引优化**: 合理的索引设计
- **查询优化**: SQL 查询优化
- **分页查询**: 大数据集分页
- **连接池**: 数据库连接复用

#### 5.3 网络优化

- **全球 CDN**: Cloudflare 全球网络
- **边缘计算**: Workers 边缘部署
- **压缩传输**: Gzip/Brotli 压缩
- **HTTP/2**: 多路复用协议

### 6. 可扩展性设计

#### 6.1 水平扩展

- **无服务器架构**: 自动弹性扩展
- **数据库分片**: 支持数据分片
- **负载均衡**: 自动负载分配
- **微服务拆分**: 服务独立扩展

#### 6.2 功能扩展

- **插件机制**: 支持功能插件
- **API 版本化**: 向后兼容设计
- **配置中心**: 动态配置管理
- **事件驱动**: 异步事件处理

### 7. 监控运维

#### 7.1 监控指标

- **性能监控**: 响应时间、吞吐量
- **错误监控**: 错误率、异常追踪
- **业务监控**: 验证次数、用户活跃度
- **资源监控**: CPU、内存、存储使用

#### 7.2 日志管理

- **结构化日志**: JSON 格式日志
- **日志等级**: DEBUG/INFO/WARN/ERROR
- **日志聚合**: 集中日志收集
- **日志分析**: 实时日志分析

#### 7.3 告警机制

- **阈值告警**: 指标超限告警
- **异常告警**: 系统异常告警
- **业务告警**: 业务指标告警
- **多渠道通知**: 邮件、短信、钉钉

### 8. 部署架构

#### 8.1 环境划分

- **开发环境**: 本地开发调试
- **测试环境**: 功能测试验证
- **预生产环境**: 性能测试
- **生产环境**: 正式服务环境

#### 8.2 CI/CD 流程

- **代码提交**: Git 版本控制
- **自动构建**: GitHub Actions
- **自动测试**: 单元测试、集成测试
- **自动部署**: 零停机部署

#### 8.3 容灾备份

- **数据备份**: 定期数据备份
- **异地容灾**: 多地域部署
- **故障恢复**: 快速故障恢复
- **业务连续性**: 服务高可用
