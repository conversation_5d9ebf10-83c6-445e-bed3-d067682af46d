# RESTful API 设计文档

## 概述

软件许可证验证系统 API，基于 RESTful 架构设计，支持用户管理、产品管理、授权管理和许可证验证。

## 基础信息

- **Base URL**: `https://api.verify.example.com/v1`
- **认证方式**: <PERSON><PERSON> (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述"
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## API 接口

### 1. 认证模块

#### 1.1 用户登录

- **POST** `/auth/login`
- **请求体**:

```json
{
  "username": "string",
  "password": "string"
}
```

- **响应**:

```json
{
  "success": true,
  "data": {
    "token": "jwt_token",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "ADMIN",
      "nickName": "管理员"
    }
  }
}
```

#### 1.2 刷新 Token

- **POST** `/auth/refresh`
- **Headers**: `Authorization: Bearer <token>`

### 2. 用户管理

#### 2.1 获取用户列表

- **GET** `/users`
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `limit`: 每页数量 (默认: 20)
  - `role`: 用户角色过滤
  - `status`: 用户状态过滤

#### 2.2 创建用户

- **POST** `/users`
- **请求体**:

```json
{
  "username": "string",
  "password": "string",
  "role": "ADMIN|DISTRIBUTOR",
  "nickName": "string",
  "wechat": "string"
}
```

#### 2.3 更新用户

- **PUT** `/users/{id}`
- **请求体**: 同创建用户（password 可选）

#### 2.4 删除用户

- **DELETE** `/users/{id}`

### 3. 产品管理

#### 3.1 获取产品列表

- **GET** `/products`
- **查询参数**:
  - `page`, `limit`: 分页参数
  - `category`: 分类过滤
  - `status`: 状态过滤

#### 3.2 创建产品

- **POST** `/products`
- **请求体**:

```json
{
  "name": "string",
  "description": "string",
  "category": "string"
}
```

#### 3.3 获取产品详情

- **GET** `/products/{id}`

#### 3.4 更新产品

- **PUT** `/products/{id}`

#### 3.5 删除产品

- **DELETE** `/products/{id}`

### 4. 产品版本管理

#### 4.1 获取版本列表

- **GET** `/products/{productId}/versions`

#### 4.2 创建版本

- **POST** `/products/{productId}/versions`
- **请求体**:

```json
{
  "version": "1.0.0",
  "versionName": "正式版",
  "description": "版本描述",
  "verifyTemplate": "{}",
  "defaultPrice": 99.0,
  "downloadLink": "https://...",
  "coverUrl": "https://...",
  "changelog": "更新日志"
}
```

#### 4.3 更新版本

- **PUT** `/versions/{id}`

#### 4.4 删除版本

- **DELETE** `/versions/{id}`

### 5. 授权管理

#### 5.1 获取授权列表

- **GET** `/authorizations`
- **查询参数**:
  - `distributorId`: 分发商 ID
  - `versionId`: 版本 ID
  - `status`: 授权状态

#### 5.2 创建授权

- **POST** `/authorizations`
- **请求体**:

```json
{
  "distributorId": 1,
  "versionId": 1,
  "customPrice": 88.0
}
```

#### 5.3 更新授权

- **PUT** `/authorizations/{id}`

#### 5.4 删除授权

- **DELETE** `/authorizations/{id}`

### 6. 许可证管理

#### 6.1 获取许可证列表

- **GET** `/licenses`
- **查询参数**:
  - `distributorId`: 分发商 ID
  - `versionId`: 版本 ID
  - `status`: 许可证状态
  - `licenseKey`: 许可证密钥

#### 6.2 生成许可证

- **POST** `/licenses`
- **请求体**:

```json
{
  "versionId": 1,
  "distributorId": 1,
  "quantity": 10
}
```

#### 6.3 激活许可证

- **POST** `/licenses/{id}/activate`
- **请求体**:

```json
{
  "verifyInstance": "machine_fingerprint"
}
```

#### 6.4 撤销许可证

- **POST** `/licenses/{id}/revoke`

### 7. 许可证验证（公开接口）

#### 7.1 验证许可证

- **POST** `/verify`
- **请求体**:

```json
{
  "licenseKey": "string",
  "verifyInstance": "string",
  "productInfo": {}
}
```

- **响应**:

```json
{
  "success": true,
  "data": {
    "valid": true,
    "productInfo": {},
    "expiresAt": "2024-12-31T23:59:59Z"
  }
}
```

## 状态码说明

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 资源冲突
- `500`: 服务器内部错误

## 权限说明

- **ADMIN**: 所有接口权限
- **DISTRIBUTOR**: 仅能访问自己的授权和许可证相关接口
- **公开接口**: `/verify` 无需认证
